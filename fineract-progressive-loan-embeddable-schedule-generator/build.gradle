/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

description = 'Fineract Progressive Loan Embeddable Schedule Generator'

apply plugin: 'com.gradleup.shadow'
apply plugin: 'java'
apply from: 'dependencies.gradle'

import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar

var requiredModuleNames = [
        'fineract-core',
        'fineract-loan',
        'fineract-progressive-loan',
]

tasks.named('shadowJar', ShadowJar) {
    dependencies {
        exclude((dep) -> !requiredModuleNames.any( (reqModName) -> dep.moduleName.contains(reqModName)))
    }
    minimize() {
        exclude("*.xml")
    }
}
