/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.integrationtests.common.organisation;

public class Currency {

    String code;
    String name;
    Integer decimalPlaces;
    String displaySymbol;
    String nameCode;
    String displayLabel;

    public Currency(String code, String name, Integer decimalPlaces, String displaySymbol, String nameCode, String displayLabel) {
        this.code = code;
        this.name = name;
        this.decimalPlaces = decimalPlaces;
        this.displaySymbol = displaySymbol;
        this.nameCode = nameCode;
        this.displayLabel = displayLabel;
    }

    public boolean isValid() {
        return (this.code != null && this.code.length() == 3) && (this.name != null && this.name.length() > 0)
                && (this.decimalPlaces != null && this.decimalPlaces >= 0)
                && (this.nameCode != null && this.nameCode.startsWith("currency."))
                && (this.displayLabel != null && this.displayLabel.length() > 0);

    }
}
