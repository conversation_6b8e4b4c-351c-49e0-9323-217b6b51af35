name: Fineract Build & Test - PostgreSQL
on: [push, pull_request]
permissions:
  contents: read
jobs:
  build:
    runs-on: ubuntu-24.04
    services:
        postgresql:
            image: postgres:17.2
            ports:
                - 5432:5432
            env:
              POSTGRES_USER: root
              POSTGRES_PASSWORD: postgres
            options: --health-cmd="pg_isready -q -d postgres -U root" --health-interval=5s --health-timeout=2s --health-retries=3
        mock-oauth2-server:
          image: ghcr.io/navikt/mock-oauth2-server:2.1.10
          ports:
            - 9000:9000
          env:
            SERVER_PORT: 9000
            JSON_CONFIG: '{ "interactiveLogin": true, "httpServer": "NettyWrapper", "tokenCallbacks": [ { "issuerId": "auth/realms/fineract", "tokenExpiry": 120, "requestMappings": [{ "requestParam": "scope", "match": "fineract", "claims": { "sub": "mifos", "scope": [ "test" ] } } ] } ] }'
    env:
        TZ: Asia/Kolkata
        DEVELOCITY_ACCESS_KEY: ${{ secrets.DEVELOCITY_ACCESS_KEY }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4
        with:
          java-version: '17'
          distribution: 'zulu'
      - name: Setup Gradle and Validate Wrapper
        uses: gradle/actions/setup-gradle@94baf225fe0a508e581a564467443d0e2379123b # v4.3.0
        with:
          validate-wrappers: true
      - name: Verify PostgreSQL connection
        run: |
            while ! pg_isready -d postgres -U root -h 127.0.0.1 -p 5432 ; do
                sleep 1
            done
      - name: Initialise databases
        run: |
            ./gradlew --no-daemon -q createPGDB -PdbName=fineract_tenants
            ./gradlew --no-daemon -q createPGDB -PdbName=fineract_default
      - name: Start LocalStack
        env:
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: localstack
          AWS_SECRET_ACCESS_KEY: localstack
          AWS_REGION: us-east-1
        run: |
          docker run -d --name localstack -p 4566:4566 -p 4510-4559:4510-4559 localstack/localstack:2.1
          sleep 10
          docker exec localstack awslocal s3api create-bucket --bucket fineract-reports
          echo "LocalStack initialization complete"
      - name: Build & Test
        env:
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: localstack
          AWS_SECRET_ACCESS_KEY: localstack
          AWS_REGION: us-east-1
          FINERACT_REPORT_EXPORT_S3_ENABLED: true
          FINERACT_REPORT_EXPORT_S3_BUCKET_NAME: fineract-reports
        run: |
            ./gradlew --no-daemon --console=plain build -x cucumber -x test -x doc
            ./gradlew --no-daemon --console=plain cucumber -x :fineract-e2e-tests-runner:cucumber
            ./gradlew --no-daemon --console=plain test -x :twofactor-tests:test -x :oauth2-test:test :fineract-e2e-tests-runner:test -PdbType=postgresql
            ./gradlew --no-daemon --console=plain :twofactor-tests:test -PdbType=postgresql
            ./gradlew --no-daemon --console=plain :oauth2-tests:test -PdbType=postgresql
      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # v4
        with:
          name: test-results
          path: |
            build/reports/
            integration-tests/build/reports/
            twofactor-tests/build/reports/
            oauth2-tests/build/reports/
      - name: Archive server logs
        if: always()
        uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # v4
        with:
          name: server-logs
          path: |
            integration-tests/build/cargo/
            twofactor-tests/build/cargo/
            oauth2-tests/build/cargo/
