<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="fineract" id="1" context="postgresql">
        <!-- Create the m_external_asset_owner_loan_product_configurable_attributes table -->
        <createTable tableName="m_external_asset_owner_loan_product_configurable_attributes">

            <column autoIncrement="true" name="id" type="BIGINT" remarks="Internal ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="loan_product_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="attribute_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="attribute_value" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_by" type="BIGINT"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="1" context="mysql">
        <!-- Create the m_external_asset_owner_loan_product_configurable_attributes table -->
        <createTable tableName="m_external_asset_owner_loan_product_configurable_attributes">
            <column autoIncrement="true" name="id" type="BIGINT" remarks="Internal ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="loan_product_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="attribute_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="attribute_value" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on_utc" type="DATETIME"/>
            <column name="last_modified_by" type="BIGINT"/>
            <column name="last_modified_on_utc" type="DATETIME"/>
        </createTable>
    </changeSet>

    <changeSet author="fineract" id="2">
        <addForeignKeyConstraint baseColumnNames="created_by"
                                 baseTableName="m_external_asset_owner_loan_product_configurable_attributes"
                                 constraintName="FK_ext_asset_owner_lp_conf_attr_created_by"
                                 deferrable="false"
                                 initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by"
                                 baseTableName="m_external_asset_owner_loan_product_configurable_attributes"
                                 constraintName="FK_ext_asset_owner_lp_conf_attr_modified_by"
                                 deferrable="false"
                                 initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="loan_product_id"
                                 baseTableName="m_external_asset_owner_loan_product_configurable_attributes"
                                 constraintName="FK_ext_asset_owner_lp_conf_attr_loan_product_id"
                                 deferrable="false"
                                 initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_product_loan" validate="true"/>
    </changeSet>

    <changeSet author="fineract" id="3">
        <insert tableName="m_permission">
            <column name="grouping" value="loan_product_attribute"/>
            <column name="code" value="CREATE_EXTERNAL_ASSET_OWNER_LOAN_PRODUCT_ATTRIBUTE"/>
            <column name="entity_name" value="EXTERNAL_ASSET_OWNER_LOAN_PRODUCT_ATTRIBUTE"/>
            <column name="action_name" value="CREATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="loan_product_attribute"/>
            <column name="code" value="UPDATE_EXTERNAL_ASSET_OWNER_LOAN_PRODUCT_ATTRIBUTE"/>
            <column name="entity_name" value="EXTERNAL_ASSET_OWNER_LOAN_PRODUCT_ATTRIBUTE"/>
            <column name="action_name" value="UPDATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
    </changeSet>
</databaseChangeLog>
