/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.organisation.staff.domain;

public enum StaffOrganisationalRoleType {

    INVALID(0, "staffOrganisationalRoleType.invalid"), //
    PROGRAM_DIRECTOR(100, "staffOrganisationalRoleType.programDirector"), //
    BRANCH_MANAGER(200, "staffOrganisationalRoleType.branchManager"), //
    FIELD_OFFICER_COORDINATOR(300, "staffOrganisationalRoleType.coordinator"), //
    FIELD_OFFICER(400, "staffOrganisationalRoleType.fieldAgent");

    private final Integer value;
    private final String code;

    StaffOrganisationalRoleType(final Integer value, final String code) {
        this.value = value;
        this.code = code;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getCode() {
        return this.code;
    }

    public static StaffOrganisationalRoleType fromInt(final Integer chargeCalculation) {
        StaffOrganisationalRoleType chargeCalculationType = StaffOrganisationalRoleType.INVALID;
        switch (chargeCalculation) {
            case 100:
                chargeCalculationType = PROGRAM_DIRECTOR;
            break;
            case 200:
                chargeCalculationType = BRANCH_MANAGER;
            break;
            case 300:
                chargeCalculationType = FIELD_OFFICER_COORDINATOR;
            break;
            case 400:
                chargeCalculationType = FIELD_OFFICER;
            break;
        }
        return chargeCalculationType;
    }
}
