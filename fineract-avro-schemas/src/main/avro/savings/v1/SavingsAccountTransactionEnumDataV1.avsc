{"name": "SavingsAccountTransactionEnumDataV1", "namespace": "org.apache.fineract.avro.savings.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "code", "type": ["null", "string"]}, {"default": null, "name": "value", "type": ["null", "string"]}, {"default": null, "name": "deposit", "type": ["null", "boolean"]}, {"default": null, "name": "dividendPayout", "type": ["null", "boolean"]}, {"default": null, "name": "withdrawal", "type": ["null", "boolean"]}, {"default": null, "name": "interestPosting", "type": ["null", "boolean"]}, {"default": null, "name": "feeDeduction", "type": ["null", "boolean"]}, {"default": null, "name": "initiateTransfer", "type": ["null", "boolean"]}, {"default": null, "name": "approveTransfer", "type": ["null", "boolean"]}, {"default": null, "name": "withdrawTransfer", "type": ["null", "boolean"]}, {"default": null, "name": "rejectTransfer", "type": ["null", "boolean"]}, {"default": null, "name": "overdraftInterest", "type": ["null", "boolean"]}, {"default": null, "name": "writtenoff", "type": ["null", "boolean"]}, {"default": null, "name": "overdraftFee", "type": ["null", "boolean"]}, {"default": null, "name": "withholdTax", "type": ["null", "boolean"]}, {"default": null, "name": "escheat", "type": ["null", "boolean"]}, {"default": null, "name": "amountHold", "type": ["null", "boolean"]}, {"default": null, "name": "amountRelease", "type": ["null", "boolean"]}]}