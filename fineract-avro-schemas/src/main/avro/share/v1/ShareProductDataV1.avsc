{"name": "ShareProductDataV1", "namespace": "org.apache.fineract.avro.share.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "name", "type": ["null", "string"]}, {"default": null, "name": "shortName", "type": ["null", "string"]}, {"default": null, "name": "description", "type": ["null", "string"]}, {"default": null, "name": "externalId", "type": ["null", "string"]}, {"default": null, "name": "currency", "type": ["null", "org.apache.fineract.avro.generic.v1.CurrencyDataV1"]}, {"default": null, "name": "totalShares", "type": ["null", "long"]}, {"default": null, "name": "totalSharesIssued", "type": ["null", "long"]}, {"default": null, "name": "unitPrice", "type": ["null", "bigdecimal"]}, {"default": null, "name": "shareCapital", "type": ["null", "bigdecimal"]}, {"default": null, "name": "minimumShares", "type": ["null", "long"]}, {"default": null, "name": "nominalShares", "type": ["null", "long"]}, {"default": null, "name": "maximumShares", "type": ["null", "long"]}, {"default": null, "name": "marketPrice", "type": ["null", {"type": "array", "items": "org.apache.fineract.avro.share.v1.ShareProductMarketPriceDataV1"}]}, {"default": null, "name": "charges", "type": ["null", {"type": "array", "items": "org.apache.fineract.avro.portfolio.v1.ChargeDataV1"}]}, {"default": null, "name": "allowDividendCalculationForInactiveClients", "type": ["null", "boolean"]}, {"default": null, "name": "lockinPeriod", "type": ["null", "int"]}, {"default": null, "name": "lockPeriodTypeEnum", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "minimumActivePeriod", "type": ["null", "int"]}, {"default": null, "name": "minimumActivePeriodForDividendsTypeEnum", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}]}