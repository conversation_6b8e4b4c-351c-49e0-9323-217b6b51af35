{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": 17175, "graphTooltip": 0, "id": 3, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(http_server_requests_seconds_count{application=\"$app_name\", uri!=\"/actuator/prometheus\"})", "instant": true, "interval": "", "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": "24h", "title": "Total Requests", "transformations": [{"id": "seriesToRows", "options": {}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 4, "y": 0}, "id": 16, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by(uri) (http_server_requests_seconds_count{application=\"$app_name\", uri!=\"/actuator/prometheus\"})", "instant": true, "interval": "", "legendFormat": "{{method}} {{uri}}", "range": true, "refId": "A"}], "timeFrom": "24h", "title": "Requests Count", "transformations": [{"id": "seriesToRows", "options": {}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}, {"id": "partitionByValues", "options": {"fields": ["Metric"]}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "id": 6, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "valueMode": "color"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by(uri)(http_server_requests_seconds_sum{application=\"$app_name\"}) / sum by(uri)(http_server_requests_seconds_count{application=\"$app_name\"})", "interval": "", "legendFormat": "{{method}} {{uri}}", "range": true, "refId": "A"}], "title": "Requests Average Duration", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 6}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(http_server_requests_seconds_count{application=\"$app_name\", outcome=\"SERVER_ERROR\"})", "interval": "", "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": "24h", "title": "Total Exceptions", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 1, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.8}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 10, "x": 4, "y": 6}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by(uri) (http_server_requests_seconds_count{application=\"$app_name\", status=~\"2.*\", uri!=\"/actuator/prometheus\"}) / sum by(uri) (http_server_requests_seconds_count{application=\"$app_name\", uri!=\"/actuator/prometheus\"})", "interval": "", "legendFormat": "{{path}}", "range": true, "refId": "A"}], "title": "Percent of 2xx Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 1, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 10, "x": 14, "y": 6}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by(uri) (http_server_requests_seconds_count{application=\"$app_name\", status=~\"5.*\", uri!=\"/actuator/prometheus\"}) / sum by(uri) (http_server_requests_seconds_count{application=\"$app_name\", uri!=\"/actuator/prometheus\"})", "interval": "", "legendFormat": "{{uri}}", "range": true, "refId": "A"}], "title": "Percent of 5xx Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 13}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(.99,sum(rate(http_server_requests_seconds_bucket{application=\"$app_name\", uri!=\"/actuator/prometheus\"}[1m])) by(uri, le))", "interval": "", "legendFormat": "{{uri}}", "range": true, "refId": "A"}], "title": "PR 99 Requests Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 13}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(.95,sum(rate(http_server_requests_seconds_bucket{application=\"$app_name\", uri!=\"/actuator/prometheus\"}[1m])) by(uri, le))", "interval": "", "legendFormat": "{{uri}}", "range": true, "refId": "A"}], "title": "PR 95 Requests Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 13}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by(uri)(rate(http_server_requests_seconds_count{application=\"$app_name\"}[1m]))", "interval": "", "legendFormat": "{{uri}}", "range": true, "refId": "A"}], "title": "Request Per Sec", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ERROR"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "INFO"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CRITICAL"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 21}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.3", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "sum by(type) (rate({compose_service=~\"fineract-.*\"} | pattern `<date> <time> <_>=<trace_id> <_>=<span_id> <_>=<trace_flags> <type> <_> --- <msg>` | type != \"\" |= \"$log_keyword\" [1m]))", "legendFormat": "{{type}}", "queryType": "range", "refId": "A"}], "title": "Log Type Rate", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "gridPos": {"h": 9, "w": 18, "x": 6, "y": 21}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": true}, "pluginVersion": "8.4.3", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "{compose_service=~\"fineract-.*\"} | pattern `<date> <time> <_>=<trace_id> <_>=<span_id> <_>=<trace_flags> <type> <_> --- <msg>` | line_format \"{{.compose_service}}\\t{{.type}}\\ttrace_id={{.trace_id}}\\t{{.msg}}\" |= \"$log_keyword\"", "hide": false, "queryType": "range", "refId": "A"}], "title": "Log of All Spring Boot Apps", "type": "logs"}], "refresh": "5s", "schemaVersion": 38, "tags": [], "templating": {"list": [{"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(application)", "hide": 0, "includeAll": false, "label": "Application Name", "multi": false, "name": "app_name", "options": [], "query": {"query": "label_values(application)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "", "value": ""}, "description": "query with keyword", "hide": 0, "label": "Log Query", "name": "log_keyword", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Fineract: <PERSON> Boot", "uid": "fineract-spring-boot", "version": 2, "weekStart": ""}