#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements. See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership. The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied. See the License for the
# specific language governing permissions and limitations
# under the License.
#

apiVersion: 1

# list of datasources that should be deleted from the database
deleteDatasources:
  - name: Prometheus
    orgId: 1

# list of datasources to insert/update depending on
# what's available in the database
datasources:
  # <string, required> name of the datasource. Required
  - uid: prometheus
    orgId: 1
    name: Prometheus
    type: prometheus
    typeName: Prometheus
    access: proxy
    url: http://prometheus:9090
    password: ''
    user: ''
    database: ''
    basicAuth: false
    isDefault: true
    jsonData:
      exemplarTraceIdDestinations:
        - datasourceUid: tempo
          name: trace_id
      httpMethod: POST
    readOnly: false
    editable: true
  - uid: tempo
    orgId: 1
    name: Tempo
    type: tempo
    typeName: Tempo
    access: proxy
    url: http://tempo:3200
    password: ''
    user: ''
    database: ''
    basicAuth: false
    isDefault: false
    jsonData:
      nodeGraph:
        enabled: true
      search:
        hide: false
      tracesToLogs:
        datasourceUid: loki
        filterBySpanID: false
        filterByTraceID: true
        mapTagNamesEnabled: false
        tags:
          - compose_service
    readOnly: false
    editable: true
  - uid: loki
    orgId: 1
    name: Loki
    type: loki
    typeName: Loki
    access: proxy
    url: http://loki:3100
    password: ''
    user: ''
    database: ''
    basicAuth: false
    isDefault: false
    jsonData:
      derivedFields:
        - datasourceUid: tempo
          matcherRegex: (?:trace_id)=(\w+)
          name: TraceID
          url: $${__value.raw}
    readOnly: false
    editable: true
