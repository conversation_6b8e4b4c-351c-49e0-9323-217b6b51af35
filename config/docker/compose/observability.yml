# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements. See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership. The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied. See the License for the
# specific language governing permissions and limitations
# under the License.
#

x-logging: &default-logging
  driver: loki
  options:
    loki-url: 'http://localhost:3100/api/prom/push'
    loki-pipeline-stages: |
      - multiline:
          firstline: '^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.\d{3}'
          max_wait_time: 3s
      - regex:
          expression: '^(?P<date>\d{4}-\d{2}-\d{2}) (?P<time>\d{2}:\d{2}:\d{2}.\d{3}) (?P<message>(?s:.*))$$'

version: "3.8"

services:
  loki:
    container_name: loki
    image: grafana/loki:2.9.2
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"

  prometheus:
    container_name: prometheus
    image: prom/prometheus:v2.47.2
    command: '--config.file=/etc/prometheus/prometheus.yml'
    ports:
      - "9090:9090"
    volumes:
      - ${PWD}/config/docker/prometheus/etc:/etc/prometheus
    logging: *default-logging

  grafana:
    container_name: grafana
    image: grafana/grafana-oss:10.2.0
    ports:
      - "3000:3000"
    volumes:
      - ${PWD}/config/docker/grafana/datasources:/etc/grafana/provisioning/datasources
      - ${PWD}/config/docker/grafana/etc/dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yaml
      - ${PWD}/config/docker/grafana/dashboards:/var/lib/grafana/dashboards
    logging: *default-logging

  tempo:
    container_name: tempo
    image: grafana/tempo:2.2.4
    # command: [ "--target=all", "--storage.trace.backend=local", "--storage.trace.local.path=/tmp/tempo", "--auth.enabled=false" ]
    command: [ "-config.file=/etc/tempo.yml" ]
    ports:
      - "14268:14268" # jaeger ingest
      - "3200:3200"   # tempo
      - "9095:9095"   # tempo grpc
      - "4317:4317"   # otlp grpc
      - "4318:4318"   # otlp http
      - "9411:9411"   # zipkin
    volumes:
      - ${PWD}/config/docker/tempo/etc/tempo.yml:/etc/tempo.yml
      - /tmp/tempo:/tmp/tempo
    logging: *default-logging
