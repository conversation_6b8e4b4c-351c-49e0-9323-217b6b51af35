/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
description = 'Fineract Rates'

apply plugin: 'java'
apply plugin: 'eclipse'

compileJava.doLast {
    def mainSS = sourceSets.main
    def source = mainSS.java.classesDirectory.get()
    copy {
        from file("src/main/resources/jpa/rates/persistence.xml")
        into "${source}/META-INF/"
    }
    javaexec {
        description = 'Performs EclipseLink static weaving of entity classes'
        def target = source
        main 'org.eclipse.persistence.tools.weaving.jpa.StaticWeave'
        args '-persistenceinfo', source, source, target
        classpath sourceSets.main.runtimeClasspath
    }
    delete {
        delete "${source}/META-INF/persistence.xml"
    }
}

// Configuration for Swagger documentation generation task
// https://github.com/swagger-api/swagger-core/tree/master/modules/swagger-gradle-plugin
import org.apache.tools.ant.filters.ReplaceTokens



configurations {
    providedRuntime // needed for Spring Boot executable WAR
    providedCompile
    compile() {
        exclude module: 'hibernate-entitymanager'
        exclude module: 'hibernate-validator'
        exclude module: 'activation'
        exclude module: 'bcmail-jdk14'
        exclude module: 'bcprov-jdk14'
        exclude module: 'bctsp-jdk14'
        exclude module: 'c3p0'
        exclude module: 'stax-api'
        exclude module: 'jaxb-api'
        exclude module: 'jaxb-impl'
        exclude module: 'jboss-logging'
        exclude module: 'itext-rtf'
        exclude module: 'classworlds'
    }
    runtime
}

apply from: 'dependencies.gradle'

// Configuration for the modernizer plugin
// https://github.com/andygoossens/gradle-modernizer-plugin
modernizer {
    ignoreClassNamePatterns = [
        '.*AbstractPersistableCustom',
        '.*EntityTables',
        '.*domain.*'
    ]
}

// If we are running Gradle within Eclipse to enhance classes with OpenJPA,
// set the classes directory to point to Eclipse's default build directory
if (project.hasProperty('env') && project.getProperty('env') == 'eclipse') {
    sourceSets.main.java.outputDir = new File(rootProject.projectDir, "fineract-rates/bin/main")
}

eclipse {
    project {
        buildCommand([ LaunchConfigHandle: "<project>/.externalToolBuilders/OpenJPA Enhance Builder.launch" ],  'org.eclipse.ui.externaltools.ExternalToolBuilder')
    }
}

/* http://stackoverflow.com/questions/19653311/jpa-repository-works-in-idea-and-production-but-not-in-gradle */
sourceSets.main.output.resourcesDir = sourceSets.main.java.classesDirectory
sourceSets.test.output.resourcesDir = sourceSets.test.java.classesDirectory

if (!(project.hasProperty('env') && project.getProperty('env') == 'dev')) {
    sourceSets {
        test {
            java {
                exclude '**/core/boot/tests/**'
            }
        }
    }
}
