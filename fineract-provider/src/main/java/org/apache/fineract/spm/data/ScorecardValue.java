/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.spm.data;

import java.time.OffsetDateTime;

public class ScorecardValue {

    private Long questionId;
    private Long responseId;
    private Integer value;
    private OffsetDateTime createdOn;

    public ScorecardValue() {

    }

    private ScorecardValue(final Long questionId, final Long responseId, final Integer value, final OffsetDateTime createdOn) {
        this.questionId = questionId;
        this.responseId = responseId;
        this.value = value;
        this.createdOn = createdOn;
    }

    public static ScorecardValue instance(final Long questionId, final Long responseId, final Integer value,
            final OffsetDateTime createdOn) {
        return new ScorecardValue(questionId, responseId, value, createdOn);
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getResponseId() {
        return responseId;
    }

    public void setResponseId(Long responseId) {
        this.responseId = responseId;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public OffsetDateTime getCreatedOn() {
        return this.createdOn;
    }

    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }

}
