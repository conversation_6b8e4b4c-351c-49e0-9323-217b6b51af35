/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.core.diagnostics.performance.sampling.core;

import static java.util.Collections.emptyMap;

import java.util.function.Supplier;

public class NoopSamplingService implements SamplingService {

    public NoopSamplingService() {}

    @Override
    public void sample(String key, Runnable r) {
        r.run();
    }

    @Override
    public <T> T sample(String key, Supplier<T> s) {
        return s.get();
    }

    @Override
    public void reset() {}

    @Override
    public SamplingData getSamplingData() {
        return new SamplingData(emptyMap());
    }
}
