/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.security.data;

import java.util.Collection;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.fineract.infrastructure.core.data.EnumOptionData;
import org.apache.fineract.useradministration.data.RoleData;

/**
 * Immutable data object for authentication. Used in case of Oauth2.
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AuthenticatedOauthUserData {

    @SuppressWarnings("unused")
    private String username;
    @SuppressWarnings("unused")
    private Long userId;
    @SuppressWarnings("unused")
    private String accessToken;
    @SuppressWarnings("unused")
    private boolean authenticated;
    @SuppressWarnings("unused")
    private Long officeId;
    @SuppressWarnings("unused")
    private String officeName;
    @SuppressWarnings("unused")
    private Long staffId;
    @SuppressWarnings("unused")
    private String staffDisplayName;
    @SuppressWarnings("unused")
    private EnumOptionData organisationalRole;
    @SuppressWarnings("unused")
    private Collection<RoleData> roles;
    @SuppressWarnings("unused")
    private Collection<String> permissions;

    @SuppressWarnings("unused")
    private boolean shouldRenewPassword;

    @SuppressWarnings("unused")
    private boolean isTwoFactorAuthenticationRequired;
}
