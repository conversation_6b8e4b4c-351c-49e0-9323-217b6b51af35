<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1" context="tenant_store_db">
        <preConditions onFail="CONTINUE">
            <sqlCheck expectedResult="1">
                SELECT count(1) FROM tenant_server_connections WHERE id = 1 and readonly_schema_password is null and readonly_schema_username is null and readonly_schema_name is null;
            </sqlCheck>
        </preConditions>
        <update tableName="tenant_server_connections">
            <column name="readonly_schema_server" value="${fineract.tenant.read-only-host}"/>
            <column name="readonly_schema_name" value="${fineract.tenant.read-only-name}"/>
            <column name="readonly_schema_server_port" value="${fineract.tenant.read-only-port}"/>
            <column name="readonly_schema_username" value="${fineract.tenant.read-only-username}"/>
            <column name="readonly_schema_password" value="${fineract.tenant.read-only-password}"/>
            <column name="readonly_schema_connection_parameters" value="${fineract.tenant.read-only-parameters}"/>
        </update>
        <customChange class="org.apache.fineract.infrastructure.core.service.migration.TenantReadOnlyPasswordEncryptionTask"/>
    </changeSet>
</databaseChangeLog>
