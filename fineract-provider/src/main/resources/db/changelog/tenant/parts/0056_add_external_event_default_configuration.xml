<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1">
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ClientActivateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ClientCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ClientRejectBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="FixedDepositAccountCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="RecurringDepositAccountCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="CentersCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="GroupsCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanAddChargeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanDeleteChargeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUpdateChargeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWaiveChargeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWaiveChargeUndoBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanProductCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanChargePaymentPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanChargePaymentPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanChargeRefundBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanCreditBalanceRefundPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanCreditBalanceRefundPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanDisbursalTransactionBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanForeClosurePostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanForeClosurePreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRefundPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRefundPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionGoodwillCreditPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionGoodwillCreditPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionMakeRepaymentPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionMakeRepaymentPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionMerchantIssuedRefundPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionMerchantIssuedRefundPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionPayoutRefundPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionPayoutRefundPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionRecoveryPaymentPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionRecoveryPaymentPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUndoWrittenOffBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWaiveInterestBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWrittenOffPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWrittenOffPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanAcceptTransferBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanAdjustTransactionBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanApplyOverdueChargeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanApprovedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanBalanceChangedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanChargebackTransactionBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanCloseAsRescheduleBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanCloseBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanCreatedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanDisbursalBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanInitiateTransferBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanInterestRecalculationBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanReassignOfficerBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRejectedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRejectTransferBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRemoveOfficerBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRescheduledDueCalendarChangeBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanRescheduledDueHolidayBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanScheduleVariationsAddedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanScheduleVariationsDeletedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanStatusChangedBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUndoApprovalBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUndoDisbursalBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUndoLastDisbursalBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanUpdateDisbursementDataBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanWithdrawTransferBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>

        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsDepositBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsWithdrawalBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsActivateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsApproveBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsCloseBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsPostInterestBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="SavingsRejectBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ShareAccountApproveBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ShareAccountCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="ShareProductDividentsCreateBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
    </changeSet>
</databaseChangeLog>
