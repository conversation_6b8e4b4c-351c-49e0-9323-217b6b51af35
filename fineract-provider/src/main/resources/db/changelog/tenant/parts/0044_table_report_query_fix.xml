<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1-mysql" context="mysql">
        <update tableName="stretchy_report">
            <column name="report_sql" value = "select details.edate entry_date, details.account_name account_name, sum(details.debit_amount) debit_amount, sum(details.credit_amount) credit_amount, details.description, ifnull(opb.openingbalance, 0) openingbalance, if(details.manual_entry = 1, details.id, '0system') transtype, if(actype in (1, 5), (sum(details.debit_amount) - sum(details.credit_amount)), (sum(details.credit_amount) - sum(details.debit_amount))) as cumulative_sum from ( select a.account_id acid1 , concat(gl.gl_code, '-', gl.name) as report_header , gl.classification_enum actype , gl.gl_code as reportid , j1.entry_date edate , concat(gl1.gl_code, '-', gl1.name) as account_name , if (j1.type_enum = 1, j1.amount, 0) as debit_amount , if (j1.type_enum = 2, j1.amount , 0) as credit_amount , j1.id , j1.office_id , j1.transaction_id , j1.type_enum , j1.office_running_balance as aftertxn , j1.description as description , j1.transaction_id as transactionid , a.manual_entry from acc_gl_journal_entry j1 inner join ( select distinct je.transaction_id tid, je.account_id, je.manual_entry from m_office o left join m_office ounder on ounder.hierarchy like concat(o.hierarchy, '%') inner join acc_gl_journal_entry je on je.office_id = ounder.id where je.account_id = ${GLAccountNO} and o.id = ${officeId} and je.entry_date between '${startDate}' and '${endDate}')a on a.tid = j1.transaction_id and j1.account_id &lt;&gt; ${GLAccountNO} left join acc_gl_account gl on gl.id = a.account_id left join acc_gl_account gl1 on gl1.id = j1.account_id order by j1.entry_date, j1.id) details left join ( select je.account_id acid2, if(aga1.classification_enum in (1, 5), (sum(if(je.type_enum = 2, ifnull(je.amount, 0), 0))- sum(if(je.type_enum = 1, ifnull(je.amount, 0), 0))), (sum(if(je.type_enum = 1, ifnull(je.amount, 0), 0))- sum(if(je.type_enum = 2, ifnull(je.amount, 0), 0)))) openingbalance from m_office o left join m_office ounder on ounder.hierarchy like concat(o.hierarchy, '%') left join acc_gl_journal_entry je on je.office_id = ounder.id left join acc_gl_account aga1 on aga1.id = je.account_id where je.entry_date &gt;= DATE_SUB('${startDate}', INTERVAL 2 day) and je.office_running_balance is not null and (o.id = ${officeId}) and je.account_id = ${GLAccountNO} group by je.account_id)opb on opb.acid2 = details.acid1 left join ( select name branchname from m_office mo where mo.id = 1)branch on details.office_id = ${officeId} group by details.edate, details.acid1, details.report_header, details.reportid, details.account_name, branch.branchname , transtype, details.description, openingbalance "/>
            <where>report_name='GeneralLedgerReport Table'</where>
        </update>
    </changeSet>
</databaseChangeLog>
