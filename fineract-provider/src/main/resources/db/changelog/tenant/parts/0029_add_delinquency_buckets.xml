<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1" context="mysql">
        <createTable tableName="m_delinquency_range">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="classification" type="VARCHAR(100)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="min_age_days" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="max_age_days" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_delinquency_bucket">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_delinquency_bucket_mappings">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="delinquency_range_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="delinquency_bucket_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_loan_delinquency_tag_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="delinquency_range_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="loan_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="addedon_date" type="DATE">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="NULL" name="liftedon_date" type="DATE" />
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="1" context="postgresql">
        <createTable tableName="m_delinquency_range">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="classification" type="VARCHAR(100)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="min_age_days" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="max_age_days" type="BIGINT">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_delinquency_bucket">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints unique="true" nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_delinquency_bucket_mappings">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="delinquency_range_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="delinquency_bucket_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
        </createTable>
        <createTable tableName="m_loan_delinquency_tag_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="delinquency_range_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="loan_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="addedon_date" type="DATE">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="NULL" name="liftedon_date" type="DATE" />
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="2">
        <addForeignKeyConstraint baseColumnNames="delinquency_range_id" baseTableName="m_delinquency_bucket_mappings"
                                 constraintName="FK_m_delinquency_range_mapping" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_delinquency_range" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="delinquency_bucket_id" baseTableName="m_delinquency_bucket_mappings"
                                 constraintName="FK_m_delinquency_bucket_mapping" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_delinquency_bucket" validate="true"/>
    </changeSet>
    <changeSet author="fineract" id="3">
        <addColumn tableName="m_product_loan">
            <column name="delinquency_bucket_id" type="BIGINT" defaultValueComputed="NULL"/>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="delinquency_bucket_id" baseTableName="m_product_loan"
                                 constraintName="FK_m_product_loan_m_delinquency_bucket" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_delinquency_bucket" validate="true"/>
    </changeSet>
    <changeSet author="fineract" id="4">
        <addForeignKeyConstraint baseColumnNames="delinquency_range_id" baseTableName="m_loan_delinquency_tag_history"
                                 constraintName="FK_m_delinquency_tags_range" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_delinquency_range" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="loan_id" baseTableName="m_loan_delinquency_tag_history"
                                 constraintName="FK_m_delinquency_tags_loan" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_loan" validate="true"/>
    </changeSet>
    <changeSet author="fineract" id="5">
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="READ_DELINQUENCY_BUCKET"/>
            <column name="entity_name" value="DELINQUENCY_BUCKET"/>
            <column name="action_name" value="READ"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="CREATE_DELINQUENCY_BUCKET"/>
            <column name="entity_name" value="DELINQUENCY_BUCKET"/>
            <column name="action_name" value="CREATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="UPDATE_DELINQUENCY_BUCKET"/>
            <column name="entity_name" value="DELINQUENCY_BUCKET"/>
            <column name="action_name" value="UPDATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="DELETE_DELINQUENCY_BUCKET"/>
            <column name="entity_name" value="DELINQUENCY_BUCKET"/>
            <column name="action_name" value="DELETE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="CREATE_DELINQUENCY_RANGE"/>
            <column name="entity_name" value="DELINQUENCY_RANGE"/>
            <column name="action_name" value="CREATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="UPDATE_DELINQUENCY_RANGE"/>
            <column name="entity_name" value="DELINQUENCY_RANGE"/>
            <column name="action_name" value="UPDATE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="organisation"/>
            <column name="code" value="DELETE_DELINQUENCY_RANGE"/>
            <column name="entity_name" value="DELINQUENCY_RANGE"/>
            <column name="action_name" value="DELETE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
    </changeSet>
</databaseChangeLog>
