<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1">
        <addColumn tableName="m_product_loan">
            <column defaultValueBoolean="false" type="boolean" name="enable_accrual_activity_posting">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="fineract" id="2">
        <addColumn tableName="m_loan">
            <column defaultValueBoolean="false" type="boolean" name="enable_accrual_activity_posting">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="3" author="fineract">
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionAccrualActivityPostBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="m_external_event_configuration">
            <column name="type" value="LoanTransactionAccrualActivityPreBusinessEvent"/>
            <column name="enabled" valueBoolean="false"/>
        </insert>
        <insert tableName="r_enum_value">
            <column name="enum_name" value="transaction_type_enum"/>
            <column name="enum_id" valueNumeric="32"/>
            <column name="enum_message_property" value="Accrual Activity"/>
            <column name="enum_value" value="Accrual Activity"/>
            <column name="enum_type" valueBoolean="false"/>
        </insert>
    </changeSet>
</databaseChangeLog>
