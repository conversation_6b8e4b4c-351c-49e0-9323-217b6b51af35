<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1">
        <insert tableName="r_enum_value">
            <column name="enum_name" value="loan_transaction_strategy_id"/>
            <column name="enum_id" valueNumeric="9"/>
            <column name="enum_message_property" value="Due penalty, interest, principal, fee, In advance penalty, interest, principal, fee"/>
            <column name="enum_value" value="Due penalty, interest, principal, fee, In advance penalty, interest, principal, fee"/>
            <column name="enum_type" valueBoolean="false"/>
        </insert>
    </changeSet>
    <changeSet id="2" author="fineract">
        <insert tableName="ref_loan_transaction_processing_strategy">
            <column name="id" valueNumeric="9"/>
            <column name="code" value="due-penalty-interest-principal-fee-in-advance-penalty-interest-principal-fee-strategy"/>
            <column name="name" value="Due penalty, interest, principal, fee, In advance penalty, interest, principal, fee"/>
            <column name="sort_order" valueNumeric="9"/>
        </insert>
    </changeSet>
</databaseChangeLog>
