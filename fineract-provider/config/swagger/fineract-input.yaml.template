openapi: 3.0.3
info:
  version: @VERSION@
  title: Apache Fineract REST API
  description: |-
    Apache Fineract is a secure, multi-tenanted microfinance platform.
    The goal of the Apache Fineract API is to empower developers to build apps on top of the Apache Fineract Platform.
    The https://cui.fineract.dev[reference app] (username: mifos, password: password) works on the same demo tenant as the interactive links in this documentation.
    Until we complete the new REST API documentation you still have the legacy documentation available https://fineract.apache.org/legacy-docs/apiLive.htm[here].
    Please check https://fineract.apache.org/docs/current[the Fineract documentation] for more information.
  contact:
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: 'http://www.apache.org/licenses/LICENSE-2.0.html'
servers:
  - url: /fineract-provider/api
  - url: https://demo.fineract.dev/fineract-provider/api
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    tenantid:        
      type: apiKey
      in: header       
      name: fineract-platform-tenantid 
security:
  - basicAuth: []
    tenantid: []
